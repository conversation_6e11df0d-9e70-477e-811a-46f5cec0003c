"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { verifyDomain } from "@/services/authService";
import { useAuthStore } from "@/stores/authStore";

// Types
interface DomainConfig {
  domain: string;
  allowed: boolean;
  config?: any;
  prompt_id?: string;
  originalDomain?: string;
  mappedDomain?: string;
}

interface DomainContextType {
  domainConfig: DomainConfig | null;
  isVerifying: boolean;
  isAllowed: boolean;
  error: string | null;
  verificationStep: string;
  retryVerification: () => void;
}

// Context
const DomainContext = createContext<DomainContextType>({
  domainConfig: null,
  isVerifying: true,
  isAllowed: false,
  error: null,
  verificationStep: 'Khởi tạo...',
  retryVerification: () => { }
});

// Hook để sử dụng context
export function useDomain() {
  return useContext(DomainContext);
}

interface DomainProviderProps {
  children: ReactNode;
}

export function DomainProvider({ children }: DomainProviderProps) {
  const [domainConfig, setDomainConfig] = useState<DomainConfig | null>(null);
  const [isVerifying, setIsVerifying] = useState(true);
  const [isAllowed, setIsAllowed] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verificationStep, setVerificationStep] = useState('Khởi tạo...');
  const setDomainConfig_AuthStore = useAuthStore((state) => state.setDomainConfig);

  const verifyCurrentDomain = async () => {
    try {
      setIsVerifying(true);
      setError(null);
      setVerificationStep('Đang xác thực domain...');

      let currentDomain = '';

      if (typeof window !== 'undefined') {
        currentDomain = window.location.hostname;

        // Nếu là localhost thì dùng domain test
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = process.env.NEXT_PUBLIC_TEST_DOMAIN || "agent.trinhxuanthuy.id.vn";
          console.log("🔄 Using test domain for localhost:", currentDomain);
        }
      }

      console.log("🔍 Verifying domain:", currentDomain);
      setVerificationStep(`Đang kiểm tra domain: ${currentDomain}`);

      const result = await verifyDomain(domain : currentDomain);

      if (!result) {
        throw new Error("Không thể xác thực domain");
      }

      if (!result) {
        setError("Domain này không được phép truy cập ứng dụng");
        setIsAllowed(false);
        setDomainConfig({
          domain: currentDomain,
          allowed: false,
          originalDomain: currentDomain
        });
        return;
      }


      setDomainConfig(result);
      setIsAllowed(true);


      setVerificationStep('Xác thực thành công!');

    } catch (error: any) {
      console.error("❌ Domain verification failed:", error);
      setError(error.message || "Lỗi xác thực domain");
      setIsAllowed(false);
      setDomainConfig({
        domain: typeof window !== 'undefined' ? window.location.hostname : '',
        allowed: false
      });
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    verifyCurrentDomain();
  }, []);

  const retryVerification = () => {
    verifyCurrentDomain();
  };

  const contextValue: DomainContextType = {
    domainConfig,
    isVerifying,
    isAllowed,
    error,
    verificationStep,
    retryVerification
  };

  return (
    <DomainContext.Provider value={contextValue}>
      {children}
    </DomainContext.Provider>
  );
}
