"use client"

import { <PERSON><PERSON>ef<PERSON> } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { signIn, useSession } from 'next-auth/react'
import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { PayloadLoginEmail, loginWithEmail, PayloadLoginGoogle, loginWithGoogle } from '@/services/authService'

import { saveTokenToCookie } from '@/lib/apiUtils';
import { isGoogleLoginEnabled as checkGoogleLoginEnabled } from '@/lib/googleAuthConfig';
import { useAuthStore } from '@/stores/authStore';
import { WebsiteInfo } from '@/components/auth/WebsiteInfo';


function storeUserInfoFromSession(session: any, userId?: string, provider: string = 'google') {
  useAuthStore.getState().setUserInfo({
    name: session.user.name || '',
    email: session.user.email,
    image: session.user.image || '',
    provider: 'google',
  });
}

// --- Component ---
export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoginEnabled, setIsGoogleLoginEnabled] = useState(true)
  const [formData, setFormData] = useState({ email: '', password: '' })
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    setIsGoogleLoginEnabled(checkGoogleLoginEnabled());
  }, []);

  // --- Google login effect ---
  useEffect(() => {
    const handleGoogleSession = async () => {
      if (status !== 'authenticated' || !session) return;

      console.log('🔍 GOOGLE SESSION DEBUG:', {
        status,
        session: session,
        backendToken: (session as any).backendToken,
        user: session.user
      });

      // Nếu đã có backendToken
      if ((session as any).backendToken) {
        const token = (session as any).backendToken;
        saveTokenToCookie(token);
        console.log('✅ GOOGLE LOGIN - Token saved to cookie and authStore');

        // Đợi một chút để authStore được cập nhật
        await new Promise(resolve => setTimeout(resolve, 200));

        toast.success('Đăng nhập Google thành công!');
        router.push('/');
        return;
      }

      // Clear auth store nếu đổi user
      try {
        const authState = useAuthStore.getState();
        if (authState.userInfo?.email && authState.userInfo.email !== session.user?.email) {
          authState.clearAll();
        }
      } catch { }

      if (session.user?.email) {
        try {
          const payload: PayloadLoginGoogle = {
            email: session.user.email,
            name: session.user.name || '',
            avatar: session.user.image || '',
            website_id: '', // Sẽ được set trong loginWithGoogle
            ref: '',
            google_id: (session as any).sub || session.user.email // Fallback to email if no sub
          };

          console.log('🔄 GOOGLE LOGIN - Payload:', payload);

          const response = await loginWithGoogle(payload);
          if (!response.error && response.data) {
            const token = response.data;
            saveTokenToCookie(token);
            useAuthStore.getState().setToken(token);
            // Lưu user info vào authStore
            storeUserInfoFromSession(session, undefined, 'google');

            // Đợi một chút để authStore được cập nhật
            await new Promise(resolve => setTimeout(resolve, 200));

            toast.success('Đăng nhập Google thành công!');
            router.push('/');
            return;
          } else {
            throw new Error(typeof response.error === 'string' ? response.error : 'Không thể lấy token từ backend');
          }
        } catch (error: any) {
          toast.error('Đăng nhập Google thất bại: ' + (error.message || 'Lỗi không xác định'));

          // Fallback: chỉ lưu user info mà không có token
          storeUserInfoFromSession(session, undefined, 'google');
        }
      }

    };
    handleGoogleSession();
  }, [status, session, router]);

  // --- Email login handler ---
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.password) { toast.error("Vui lòng nhập đầy đủ email và mật khẩu"); return; }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) { toast.error("Email không hợp lệ"); return; }

    if (isLoading) { return; }
    setIsLoading(true);

    try {
      const payload: PayloadLoginEmail = {
        email: formData.email,
        password: formData.password,
      };

      const response = await loginWithEmail(payload);

      if (!response.error) {
        // Lưu token vào cookie và authStore
        const token = response.data;
        if (token) {
          saveTokenToCookie(token);
          // Đợi một chút để authStore được cập nhật
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        toast.success("Đăng nhập thành công");
        router.push("/");
      } else { toast.error(response.error || "Đăng nhập thất bại, vui lòng thử lại"); }
    } catch (error: any) {
      const errorMessage = error.message || "Đã xảy ra lỗi, vui lòng thử lại";
      toast.error(errorMessage);
    } finally { setIsLoading(false); }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true)
    try {
      toast.info('Đang chuyển hướng đến Google...')
      const result = await signIn('google', {
        callbackUrl: '/',
        redirect: false
      })

      if (result?.error) {
        toast.error('Đăng nhập Google thất bại: ' + result.error)
      } else if (result?.ok) {
        toast.success('Đăng nhập Google thành công!')
      }
    } catch (error) {
      toast.error('Đăng nhập Google thất bại. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen w-full bg-white dark:bg-gray-900">
      <a
        className="absolute top-6 left-6 z-10 hidden cursor-pointer lg:block"
        onClick={e => {
          e.preventDefault();
          if (window.history.length > 1) {
            router.back();
          } else {
            router.push('/');
          }
        }}
        href="/"
      >
        <button
          type="button"
          aria-label="Quay lại"
          title="Quay lại"
          className="inline-flex items-center justify-center h-10 w-10 rounded-full text-sm font-medium transition hover:bg-accent hover:text-accent-foreground dark:hover:bg-gray-900 focus-visible:ring-2 focus-visible:ring-ring/50"
        >
          <ArrowLeft className="h-5 w-5 dark:text-slate-200" />
        </button>
      </a>
      {/* Sidebar image (left) */}
      <div className="hidden md:block md:w-1/2 relative overflow-hidden">
        <Image
          src="/left-image.png"
          alt="FChat.ai - Trợ lý AI thông minh"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/10 to-transparent"></div>
      </div>
      {/* Login form (right) */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 min-h-screen">
        <div className="w-full max-w-md mx-auto p-8 flex flex-col items-center space-y-6">
          {/* Website Info từ prompt.website */}
          <WebsiteInfo />

          <h2 className="text-2xl font-semibold text-center mb-2">Đăng nhập</h2>

          {/* Google Login - Conditionally rendered based on domain config */}
          {isGoogleLoginEnabled ? (
            <>
              <button
                type="button"
                onClick={handleGoogleLogin}
                disabled={isLoading}
                className="w-full h-12 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center gap-3 font-medium"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5"><g><path fill="#4285F4" d="M24 9.5c3.54 0 6.7 1.22 9.19 3.23l6.85-6.85C35.64 2.36 30.23 0 24 0 14.82 0 6.71 5.82 2.69 14.09l7.98 6.19C12.13 13.19 17.57 9.5 24 9.5z" /><path fill="#34A853" d="M46.1 24.55c0-1.64-.15-3.22-.43-4.74H24v9.01h12.42c-.54 2.9-2.18 5.36-4.64 7.04l7.19 5.6C43.93 37.13 46.1 31.36 46.1 24.55z" /><path fill="#FBBC05" d="M10.67 28.28a14.5 14.5 0 0 1 0-8.56l-7.98-6.19A23.94 23.94 0 0 0 0 24c0 3.82.92 7.43 2.69 10.65l7.98-6.19z" /><path fill="#EA4335" d="M24 48c6.23 0 11.44-2.06 15.25-5.6l-7.19-5.6c-2.01 1.35-4.6 2.15-8.06 2.15-6.43 0-11.87-3.69-13.33-8.65l-7.98 6.19C6.71 42.18 14.82 48 24 48z" /><path fill="none" d="M0 0h48v48H0z" /></g></svg>
                Đăng nhập với tài khoản Google
              </button>
              <div className="flex items-center gap-2 w-full">
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700" />
                <span className="text-xs text-gray-400">Hoặc</span>
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700" />
              </div>
            </>
          ) : (
            <div className="w-full text-center">
              <p className="text-sm text-gray-500 mb-4">
                Đăng nhập bằng email và mật khẩu
              </p>
            </div>
          )}
          <form onSubmit={handleEmailLogin} className="space-y-4 w-full">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                placeholder="Nhập email"
                disabled={isLoading}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1">Mật khẩu</label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm pr-10"
                  placeholder="Nhập mật khẩu"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute inset-y-0 right-2 flex items-center text-gray-400 hover:text-gray-600 text-sm"
                  onClick={() => setShowPassword(v => !v)}
                  aria-label={showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-5.523 0-10-4.477-10-10 0-1.657.403-3.22 1.125-4.575m1.875-2.425A9.956 9.956 0 0112 3c5.523 0 10 4.477 10 10 0 1.657-.403-3.22-1.125-4.575m-1.875 2.425A9.956 9.956 0 0112 21c-2.21 0-4.267-.72-5.925-1.95M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm7.5 0a9.956 9.956 0 01-1.125 4.575m-1.875 2.425A9.956 9.956 0 0112 21c-2.21 0-4.267-.72-5.925-1.95m-1.875-2.425A9.956 9.956 0 012.5 12c0-1.657.403-3.22 1.125-4.575m1.875-2.425A9.956 9.956 0 0112 3c2.21 0 4.267.72 5.925 1.95" /></svg>
                  )}
                </button>
              </div>
            </div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full h-10 rounded-3xl bg-green-500 hover:bg-green-600 text-white font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
            >
              {isLoading && (
                <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline-block mr-2"></span>
              )}
              Đăng nhập
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
