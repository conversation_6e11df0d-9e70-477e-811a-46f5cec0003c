// --- Types ---
export interface UserProfile {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface ShopProfile {
  _id: string;
  shop_id?: string;
}

export interface PackageProfile {
  credit: number;
  credit_use: number;
}

export interface ProfileData {
  user: UserProfile;
  shop?: ShopProfile;
  package?: PackageProfile;
}

export interface UserProfileResponse {
  error: boolean;
  status: number;
  msg: string;
  data: ProfileData;
}