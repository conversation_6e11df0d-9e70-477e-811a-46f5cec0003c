import { useMemo } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { useAppStore } from '@/stores/appStore';

/**
 * Hook to get authentication data in a consistent way
 * Replaces scattered token/shop_id/website_id retrieval logic
 */
export const useAuthData = () => {
    const authStore = useAuthStore();
    const appStore = useAppStore();

    const authData = useMemo(() => ({
        // Core auth data
        token: authStore.getToken(),
        user_id: authStore.user_id,
        shop_id: authStore.shop_id,
        prompt_id: authStore.prompt_id,
        website_id: appStore.getWebsiteId(),

        // Auth state
        isAuthenticated: authStore.isAuthenticated,
        userInfo: authStore.userInfo,

        // Validation helpers
        hasToken: !!authStore.getToken(),
        hasRequiredData: !!(authStore.getToken() && authStore.user_id && authStore.shop_id),

        // Actions
        clearAuth: authStore.clearAll,
        setToken: authStore.setToken,
        setUserData: authStore.setUserData
    }), [
        authStore.user_id,
        authStore.shop_id,
        authStore.prompt_id,
        authStore.isAuthenticated,
        authStore.userInfo,
        authStore.getToken,
        authStore.clearAll,
        authStore.setToken,
        authStore.setUserData,
        appStore.getWebsiteId
    ]);

    return authData;
};

/**
 * Hook to get API headers for requests
 */
export const useApiHeaders = () => {
    const { token, shop_id } = useAuthData();

    return useMemo(() => {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };

        if (token) {
            headers.token = token;
        }

        if (shop_id) {
            headers.shop_id = shop_id;
        }

        return headers;
    }, [token, shop_id]);
};

/**
 * Hook to create chat payload
 */
export const useChatPayload = () => {
    const { user_id, shop_id, website_id } = useAuthData();

    return useMemo(() => ({
        createPayload: (conversationId: string, query: string, promptId: string) => ({
            conversation_id: conversationId || '',
            query,
            prompt_id: promptId || '',
            shop_id: shop_id || '',
            version: 'gpt-4o-mini',
            user_id: user_id || '',
            website_id: website_id || ''
        })
    }), [user_id, shop_id, website_id]);
};
