"use client"

import React, { useEffect, useRef, useState } from 'react'
import { LoadingContent } from '@/components/ui/LoadingSpinner'
import { useMessageChat } from '@/hooks/useChat'
import { useCreditMonitor } from '@/hooks/useCreditMonitor'
import { SendChat } from './sendChat'
import { MessageList } from './MessageList'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'
import { useConversationSwitch } from '@/hooks/useConversationState';

export interface ChatInterfaceProps {
  conversationId: string
  promptId: string
  shopId: string
  userId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
}

export function ChatInterface({ conversationId: propConversationId, promptId, shopId, userId, onConversationUpdate }: ChatInterfaceProps) {
  const [currentConversationId, setCurrentConversationId] = useState(propConversationId);
  const lastRequestedId = useRef<string>(propConversationId)
  const { websiteInfo, isLoading } = useWebsiteInfo()
  const { switchConversation } = useConversationSwitch()

  useCreditMonitor()

  const {
    messages,
    setMessages,
    isLoadingMessages,
    messagesEndRef,
    hasToken
  } = useMessageChat({ conversationId: currentConversationId })

  useEffect(() => {
    const handleClearMessages = (event: CustomEvent) => {
      setMessages([]);
    };
    window.addEventListener('clearMessages', handleClearMessages as EventListener);
    return () => window.removeEventListener('clearMessages', handleClearMessages as EventListener);
  }, [setMessages]);

  useEffect(() => {
    const handleConversationChange = (event: CustomEvent) => {
      const newConversationId = event.detail.conversationId;
      if (newConversationId !== currentConversationId) {
        setMessages([]);
        setCurrentConversationId(newConversationId);
        switchConversation(newConversationId); // Đồng bộ với Zustand
      }
    };
    window.addEventListener('conversationChange', handleConversationChange as EventListener);
    return () => window.removeEventListener('conversationChange', handleConversationChange as EventListener);
  }, [currentConversationId, setMessages, switchConversation]);

  useEffect(() => {
    if (propConversationId !== currentConversationId) {
      setMessages([]);
      setCurrentConversationId(propConversationId);
      switchConversation(propConversationId);
    }
  }, [propConversationId, currentConversationId, setMessages, switchConversation]);

  useEffect(() => {
    if (currentConversationId === '0') {
      setMessages([]);
    }
  }, [currentConversationId, setMessages]);

  useEffect(() => {
    const handlePopState = () => {
      const pathParts = window.location.pathname.split('/');
      const urlConversationId = pathParts[pathParts.length - 1];
      if (urlConversationId && urlConversationId !== currentConversationId) {
        setCurrentConversationId(urlConversationId);
        switchConversation(urlConversationId);
      }
    };
    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentConversationId, switchConversation]);

  React.useEffect(() => {
    if (messagesEndRef?.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  if (!hasToken) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingContent text="Đang tải..." />
      </div>
    )
  }

  return (
    <>
      <div className="relative flex-1">
        <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          messagesEndRef={messagesEndRef}
        />
      </div>
      <div className='flex flex-col w-full transition-all duration-300 md:p-4 md:pb-0'>
        <SendChat
          conversationId={currentConversationId}
          promptId={promptId}
          onConversationUpdate={(oldId, newId, newName) => {
            setCurrentConversationId(newId);
            switchConversation(newId);
            onConversationUpdate?.(oldId, newId, newName);
          }}
          messages={messages}
          setMessages={setMessages}
        />
        <p className='my-1 px-2 text-center text-xs text-gray-500 md:my-2 dark:text-gray-400'>{websiteInfo.meta_title} có thể mắc sai sót, vui lòng xác thực các thông tin quan trọng</p>
      </div>
    </>
  )
}

export default ChatInterface;