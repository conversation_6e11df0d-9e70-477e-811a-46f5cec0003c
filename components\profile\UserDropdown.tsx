"use client";
import { useState } from 'react';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { performCompleteLogout } from '@/lib/logoutUtils';
import { useToast } from '@/hooks/use-toast';

type UserDropdownProps = {
    user: {
        name?: string;
        email?: string;
        avatar?: string;
        credit?: number;
        credit_use?: number;
    };
};

export default function UserDropdown({ user }: UserDropdownProps) {
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const { toast } = useToast();

    const creditUsed = user.credit_use || 0;
    const creditTotal = user.credit || 0;
    const percent = Math.round((creditUsed / creditTotal) * 100)
    const initial = user.name?.[0]?.toUpperCase() || user.email?.[0]?.toUpperCase() || 'U';

    const handleLogout = async () => {
        if (isLoggingOut) return; // Prevent double-click

        setIsLoggingOut(true);

        try {
            toast({
                title: "Đang đăng xuất...",
                description: "Vui lòng chờ trong giây lát",
            });

            await performCompleteLogout();
        } catch (error) {
            toast({
                title: "Lỗi đăng xuất",
                description: "Có lỗi xảy ra khi đăng xuất. Vui lòng thử lại.",
                variant: "destructive",
            });
            setIsLoggingOut(false);
        }
    };
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {user.avatar ? (
                    <img src={user.avatar} alt={user.name} className="ml-2 w-12 h-12 rounded-full object-cover" />
                ) : (
                    <button type="button" className={`ml-2 flex items-center justify-center w-12 h-12 rounded-full text-xl font-semibold text-white focus:outline-none bg-green-500`}>
                        {initial}
                    </button>
                )}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-auto min-w-80 p-0">
                <div className="p-4 flex flex-col ">
                    <div className='flex flex-row gap-2 align-middle'>
                        {user.avatar ? (
                            <img src={user.avatar} alt={user.name} className="w-12 h-12 rounded-full object-cover" />
                        ) : (
                            <div className="w-12 h-12 rounded-full flex text-2xl font-bold text-white mb-2 bg-green-500">{initial}</div>
                        )}
                        <div className='flex flex-col align-middle'>
                            <div className="text-base font-semibold text-gray-900 dark:text-slate-100 truncate w-full">{user.name}</div>
                            <div className="text-sm text-gray-500 dark:text-slate-400 truncate w-full">{user.email}</div>
                        </div>
                    </div>

                    <div className="w-full mt-3">
                        <div className="flex justify-between text-sm text-gray-700 dark:text-slate-300 mb-2">
                            <span>Đã sử dụng: {creditUsed}/{creditTotal}</span>
                            <span>{percent}%</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                                className="h-full bg-green-500 transition-all duration-300"
                                style={{ width: `${percent}%` }}
                            />
                        </div>
                    </div>
                </div>

                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className={`flex items-center gap-2 cursor-pointer ${isLoggingOut
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-red-600 hover:text-red-700'
                        }`}
                >
                    {isLoggingOut ? (
                        <div className="mr-2 w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="mr-2">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H7a2 2 0 01-2-2V7a2 2 0 012-2h4a2 2 0 012 2v1" />
                        </svg>
                    )}
                    {isLoggingOut ? 'Đang đăng xuất...' : 'Đăng xuất'}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu >
    );
} 