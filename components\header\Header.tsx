"use client"

import { getCookie } from 'cookies-next/client';
import { useSession } from 'next-auth/react';
import { useEffect, useState, useCallback } from 'react';

import UserDropdown from '@/components/profile/UserDropdown';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { WebsiteBranding } from '@/components/ui/WebsiteBranding';
import { getAvatarFromProfile } from '@/lib/avatarUtils';
import { getUserProfile } from '@/services/userService';
import { useAuthStore } from '@/stores/authStore';
import { useAppStore } from '@/stores/appStore';
import { COOKIE_KEYS } from '@/types/auth';
import { ProfileData } from '@/types/profile';


export default function Header() {
    const { data: session, status } = useSession();
    const [profile, setProfile] = useState<ProfileData | null>(null);

    const getUserId = () => getCookie(COOKIE_KEYS.USER_ID) as string;
    const getShopId = () => getCookie(COOKIE_KEYS.SHOP_ID) as string;
    const getPromptId = () => useAppStore.getState().getPromptId();
    const getWebsiteId = () => useAppStore.getState().getWebsiteId();

    //Fallback profile cho demo/session
    const getFallbackProfile = (sessionInfo?: { name?: string; email?: string; avatar?: string }): ProfileData => ({
        user: {
            _id: '',
            name: sessionInfo?.name || '',
            email: sessionInfo?.email || '',
            avatar: sessionInfo?.avatar,
        },
        package: {
            credit: 1000,
            credit_use: 0,
        },
    });

    // Helper: Merge API profile với session
    const mergeProfile = (
        apiProfile: any,
        sessionInfo?: { name?: string; email?: string; avatar?: string }
    ): ProfileData => ({
        ...apiProfile,
        user: {
            ...apiProfile.user,
            ...(sessionInfo && {
                name: sessionInfo.name || apiProfile.user?.name,
                email: sessionInfo.email || apiProfile.user?.email,
                avatar: sessionInfo.avatar || apiProfile.user?.avatar,
            }),
        },
    });

    // Main: Load user profile
    const loadUserProfile = useCallback(async () => {
        // Lấy session info nếu có
        const sessionInfo = session?.user
            ? {
                name: session.user.name ?? undefined,
                email: session.user.email ?? undefined,
                avatar: session.user.image ?? undefined,
            }
            : undefined;

        // Check demo user
        if (typeof window !== 'undefined' && sessionStorage.getItem('demo_logged_in') === 'true') {
            const demoUserInfo = sessionStorage.getItem('demo_user_info');
            if (demoUserInfo) {
                try {
                    const userInfo = JSON.parse(demoUserInfo);
                    setProfile({
                        user: {
                            _id: '',
                            name: userInfo.name,
                            email: userInfo.email,
                            avatar: undefined,
                        },
                        package: {
                            credit: 500,
                            credit_use: 0,
                        },
                    });
                } catch { }
            }
            return;
        }

        // Lấy token, shop_id, user_id từ cookie hoặc Zustand
        const token = getCookie(COOKIE_KEYS.TOKEN) as string || useAuthStore.getState().getToken();
        let shop_id = getShopId();
        let user_id = getUserId();

        // Lấy prompt_id, website_id từ appStore nếu cần
        const prompt_id = getPromptId();
        const website_id = getWebsiteId();

        if (!token) {
            setProfile(getFallbackProfile(sessionInfo));
            return;
        }

        // Lấy profile từ API
        try {
            const profileRes = await getUserProfile();
            if (profileRes && !profileRes.error && profileRes.data) {
                const mergedProfile = mergeProfile(profileRes.data, sessionInfo);
                setProfile(mergedProfile);
                setProfile(getFallbackProfile(sessionInfo));
            }
        } catch {
            setProfile(getFallbackProfile(sessionInfo));
        }
    }, [session]);

    // Reload profile khi token hoặc session thay đổi
    useEffect(() => {
        if (status === 'loading') return;
        loadUserProfile();
    }, [status, session, loadUserProfile]);

    // Reload profile khi token được set lại
    useEffect(() => {
        const handleTokenSet = () => loadUserProfile();
        window.addEventListener('tokenSet', handleTokenSet);
        return () => window.removeEventListener('tokenSet', handleTokenSet);
    }, [loadUserProfile]);

    return (
        <header className="sticky top-0 z-20 w-full border-b border-gray-200 bg-white dark:border-slate-700 dark:bg-gray-900 flex items-center justify-between px-4 py-1.5">
            <div className="flex items-center gap-3">
                <SidebarTrigger className="-ml-1" />
                <WebsiteBranding
                    logoSize={46}
                    showTitle={true}
                    className="font-bold text-gray-800 sm:text-lg md:text-xl dark:text-slate-100"
                />
            </div>

            <div className="flex items-center justify-end gap-3 min-w-[200px]">
                <ThemeToggle />
                <UserDropdown
                    user={(() => {
                        if (!profile) return {};
                        const avatarUrl = getAvatarFromProfile(profile, profile.user.avatar);
                        return {
                            name: profile.user.name,
                            email: profile.user.email,
                            avatar: avatarUrl,
                            credit: profile.package?.credit || 0,
                            credit_use: profile.package?.credit_use || 0,
                        };
                    })()}
                />
            </div>
        </header>
    );
}
