import { decrypt<PERSON>esC<PERSON> } from '@/lib/crypto';
import { EP } from "@/configs/constants/api";
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';
import {
    apiCall,
    fetchWithStream,
    handleApiError,
    BASE_API_URL,
    getPromptIdWithFallback,
    getStoredToken,
    getStoredShopId,
    createApiClient
} from "@/lib/apiUtils";

// Re-export for convenience
export { getPromptIdWithFallback } from "@/lib/apiUtils";
import {
    DataResponseType,
    DomainVerificationResponse,
    DomainConfig,
    Message,
    Conversation,
    ChatRequest,
    CreateConversationRequest,
    UserProfile
} from "@/types";

// Re-export types for convenience
export type {
    Message,
    Conversation,
    ChatRequest,
    CreateConversationRequest,
    UserProfile,
    DomainConfig
} from "@/types";



/**
 * Get user profile (GET profile)
 * Uses token from cookie as per requirement
 * @param shop_id Shop ID
 * @returns User profile data
 */
export const getUserProfile = async (shop_id?: string): Promise<DataResponseType<UserProfile>> => {
    try {

        const token = getCookie(COOKIE_KEYS.TOKEN) as string;
        if (!token) {
            throw new Error('No authentication token found');
        }

        // Prepare query params
        const response = await apiCall<UserProfile>(
            'GET',
            [EP.API, EP.V1, EP.USER, EP.PROFILE],
            undefined,

        );
        return response;
    } catch (error) {
        return handleApiError(error, 'Get User Profile');
    }
};


/**
 * Update conversation name
 * POST /api/v1/conversation/update
 * @param conversationId Conversation ID
 * @param name New conversation name
 * @param token Authentication token
 * @returns Update response
 */
export const updateConversationName = async (
    conversationId: string,
    name: string,
    token?: string
): Promise<DataResponseType<any>> => {
    if (!conversationId || typeof conversationId !== 'string') {
        throw new Error(`Invalid conversationId provided: ${JSON.stringify(conversationId)}`);
    }

    if (!name || typeof name !== 'string') {
        throw new Error(`Invalid name provided: ${JSON.stringify(name)}`);
    }

    const finalToken = token || getStoredToken();
    const finalShopId = getStoredShopId();

    if (!finalToken) {
        throw new Error('No authentication token available');
    }

    try {
        const response = await apiCall<any>(
            'POST',
            [EP.API, EP.V1, EP.CONVERSATION, EP.UPDATE],
            {
                id: conversationId,
                name: name
            },
            undefined,
            finalToken,
            finalShopId || undefined
        );
        return response;
    } catch (error) {
        return handleApiError(error, 'Update Conversation Name');
    }
};

/**
 * Create new conversation
 * POST /api/v1/message/conversation?conversation_id=0
 * @param token Authentication token
 * @returns New conversation response
 */
export const createNewConversationMessage = async (
    token?: string
): Promise<DataResponseType<any>> => {
    const finalToken = token || getStoredToken();
    const finalShopId = getStoredShopId();

    if (!finalToken) {
        throw new Error('No authentication token available');
    }

    try {
        const response = await apiCall<any>(
            'POST',
            [EP.API, EP.V1, EP.MESSAGE, EP.CONVERSATION],
            undefined, // No body data needed
            {
                conversation_id: '0' // Default ID for new conversation
            },
            finalToken,
            finalShopId || undefined
        );

        response.data && typeof response.data === 'object'
        return response;
    } catch (error) {
        return handleApiError(error, 'Create New Conversation');
    }
};

/**
 * Get prompt list (conversations) - GET conversations
 * Uses token from cookie as per requirement
 * @param prompt_id Prompt ID
 * @param page Page number
 * @param limit Items per page
 * @param _shop_id Shop ID (optional)
 * @returns List of conversations
 */
export const getPromptList = async<T>(
    prompt_id: string,
    page: number = 1,
    limit: number = 10,
    _shop_id?: string
): Promise<DataResponseType<T>> => {
    // Get token from cookie
    const token = getCookie(COOKIE_KEYS.TOKEN) as string;
    if (!token) {
        throw new Error('No authentication token found');
    }

    // Get prompt_id from store if not provided
    let finalPromptId = prompt_id;
    if (!finalPromptId) {
        try {
            const { useAppStore } = require('@/stores/appStore');
            finalPromptId = useAppStore.getState().getPromptId();
        } catch (e) { }
    }

    if (!finalPromptId || typeof finalPromptId !== 'string') {
        throw new Error(`Invalid prompt_id provided: ${JSON.stringify(finalPromptId)}`);
    }

    try {
        const result = await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.CONVERSATION, EP.PROMPT, EP.LIST],
            undefined,
            {
                prompt_id: finalPromptId,
                page: page.toString(),
                limit: limit.toString()
            },
            token,
            undefined
        );
        return result;
    } catch (error: any) {
        return handleApiError(error, 'Get Prompt List');
    }
};

/**
 * Get conversation messages
 * @param conversation_id Conversation ID
 * @param token Authentication token
 * @returns Array of messages
 */
export const getConversationMessages = async (
    conversation_id: string,
    token?: string,
): Promise<Message[]> => {
    // Always get token from Zustand/cookie if not provided
    let finalToken = token;
    if (!finalToken) {
        try {
            const { useAuthStore } = require('@/stores/authStore');
            finalToken = useAuthStore.getState().getToken();
        } catch (e) { }
    }
    if (!finalToken) {
        throw new Error('Token is required for API access');
    }

    // Handle demo, new conversation, or invalid conversation IDs
    if (!conversation_id || conversation_id === '0' || conversation_id === 'demo_1' || conversation_id.startsWith('demo_')) {
        if (conversation_id === '0') {
        } else {
            console.warn('Demo or invalid conversation ID detected:', conversation_id);
        }
        return []; // Return empty array for new/demo conversations
    }

    try {

        // Use the new API endpoint directly
        const api = createApiClient(finalToken);
        const url = `/api/v1/message/conversation?conversation_id=${conversation_id}`;

        const response = await api.get<DataResponseType<Message[]>>(url, {
            headers: {
                'token': finalToken
            }
        });

        // Check if response indicates success
        if (response.data.error === false && response.data.status === 200) {
            return response.data.data || [];
        } else {
            return [];
        }
    } catch (error: any) {
        console.error('Error fetching conversation messages:', error.response?.data || error.message);

        // If it's a 500 error, return empty array instead of throwing
        if (error.response?.status === 500) {
            console.warn('Server error 500, returning empty messages array');
            return [];
        }

        throw new Error(`Failed to fetch messages: ${error.response?.data?.msg || error.message}`);
    }
};

/**
 * Send chat message (streaming)
 * @param chatRequest Chat request data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @param user_id User ID
 * @param website_id Website ID
 * @returns ReadableStream for streaming response
 */
export const sendChatMessage = async (
    chatRequest: ChatRequest,
    token?: string,
    shop_id?: string,
    user_id?: string,
    website_id?: string,
    abortController?: AbortController
): Promise<ReadableStream<Uint8Array> | null> => {
    let finalToken = undefined;
    let finalShopId = undefined;
    let finalUserId = undefined;

    try {
        // Ưu tiên lấy từ cookie trước
        finalToken = getCookie(COOKIE_KEYS.TOKEN) as string;
        finalShopId = getCookie(COOKIE_KEYS.SHOP_ID) as string;
        finalUserId = getCookie(COOKIE_KEYS.USER_ID) as string;
    } catch (e) {
        finalToken = undefined;
        finalShopId = undefined;
        finalUserId = undefined;
    }

    // Nếu không có trong cookie thì lấy từ zustand state
    if (!finalToken || !finalShopId || !finalUserId) {
        try {
            const { useAuthStore } = require('@/stores/authStore');
            const state = useAuthStore.getState();
            finalToken = finalToken || state.getToken?.();
            finalShopId = finalShopId || state.shop_id;
            finalUserId = finalUserId || state.user_id;
        } catch (e) { }
    }

    // Nếu vẫn không có user_id thì lấy từ chatRequest
    if (!finalUserId) {
        finalUserId = chatRequest.user_id;
    }

    if (!finalToken) {
        throw new Error('Token is required for sending messages');
    }

    // Use local API route as proxy to avoid CORS issues
    const url = '/api/chat';

    // Include token and shop_id in the request body for the proxy
    // Lấy website_id từ global state nếu chưa có
    const proxyRequest = {
        ...chatRequest,
        token: finalToken,
        shop_id: finalShopId,
        user_id: finalUserId,
    };

    try {
        return await fetchWithStream(
            url,
            proxyRequest,
            finalToken,
            finalShopId || undefined,
            abortController?.signal
        );
    } catch (error) {
        console.error('❌ Error sending chat message:', error);
        return null;
    }
};


/**
 * Create new conversation
 * @param request Conversation request data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Created conversation or null
 */
export const createNewConversation = async (
    request: CreateConversationRequest,
    token?: string,
    shop_id?: string
): Promise<Conversation | null> => {
    try {
        const response = await apiCall<Conversation>(
            'POST',
            [EP.API, EP.V1, EP.CONVERSATION, EP.CREATE],
            request,
            undefined,
            token,
            shop_id
        );

        return response.data || null;
    } catch (error: any) {
        console.error('❌ Error creating new conversation:', error.response?.data || error.message);
        return null;
    }
};

/**
 * Fetch prompt conversations
 * @param prompt_id Prompt ID
 * @param page Page number
 * @param limit Items per page
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns List of conversations
 */
export async function fetchPromptconversations(prompt_id?: string, page: number = 1, limit: number = 10, token?: string, _shop_id?: string) {
    try {
        // Use utility function to get prompt_id with proper fallback
        const finalPromptId = getPromptIdWithFallback(prompt_id);

        const res = await getPromptList(finalPromptId, page, limit, token);

        // Check if API call was successful
        if (res && res.error === false && res.status === 200) {

            let conversations: any[] = [];

            if (res.data) {
                if (Array.isArray(res.data)) {
                    // Case 1: Direct array
                    conversations = res.data;
                } else if (res.data && typeof res.data === 'object' && Array.isArray((res.data as any).conversations)) {
                    // Case 2: Nested object
                    conversations = (res.data as any).conversations;
                }
            }

            if (conversations.length > 0) {
                const mappedConversations = conversations.map((item: Conversation) => ({
                    id: item._id,
                    icon: '/favicon.ico',
                    title: item.name || 'Chủ đề AI',
                    messages: [],
                    conversation: item
                }));

                return mappedConversations;
            } else {
                // API successful but no conversations data - return empty array
                return [];
            }
        } else {
            throw new Error(res?.msg || 'API call failed');
        }
    } catch (err: any) {
        console.error('Lỗi lấy danh sách chủ đề:', err);
        throw new Error(err.message || 'Không thể tải danh sách cuộc trò chuyện');
    }
}
