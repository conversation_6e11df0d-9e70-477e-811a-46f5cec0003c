import { useEffect, useRef } from 'react'
import { useUserCredits } from '@/hooks/useAuthStore'
import { showLowCreditWarning, calculateCreditInfo } from '@/lib/creditUtils'

/**
 * Hook để theo dõi credit và hiển thị cảnh báo khi cần thiết
 */
export const useCreditMonitor = () => {
  const { credit, credit_use } = useUserCredits()
  const lastWarningRef = useRef<number>(0)
  const WARNING_COOLDOWN = 5 * 60 * 1000 // 5 phút

  useEffect(() => {
    if (!credit || credit <= 0) return

    const creditInfo = calculateCreditInfo(credit, credit_use)
    const now = Date.now()

    if (creditInfo.isLowCredit && (now - lastWarningRef.current) > WARNING_COOLDOWN) {
      showLowCreditWarning(credit, credit_use)
      lastWarningRef.current = now
    }
  }, [credit, credit_use])

  return {
    credit,
    credit_use,
    creditInfo: calculateCreditInfo(credit, credit_use)
  }
}
