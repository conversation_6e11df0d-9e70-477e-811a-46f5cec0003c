"use client"

import { useEffect } from 'react';

import { useAuthStore } from '@/stores/authStore';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider - Initializes auth store from cookies on app start
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const initializeFromCookies = useAuthStore((state) => state.initializeFromCookies);

  useEffect(() => {
    // Initialize auth store from cookies
    initializeFromCookies();

    // Listen for token changes
    const handleTokenSet = () => {
      console.log('🔄 AUTH PROVIDER - Token set event received, re-initializing...');
      initializeFromCookies();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('tokenSet', handleTokenSet);

      return () => {
        window.removeEventListener('tokenSet', handleTokenSet);
      };
    }
  }, [initializeFromCookies]);

  return <>{children}</>;
}
